export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  statusCode?: number;
}

export interface LoginCredentials {
  email: string;
  password?: string;
  code?: string;
}

export interface RegisterData extends LoginCredentials {
  name?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  message?: string;
  token?: string;
}

export interface AuthClientConfig {
  // 必需配置 - 不允许使用默认值
  authServiceApiUrl: string;
  authServiceRedirectUrl: string;
  // 可选配置 - 可以有合理默认值
  sensitiveHostnames?: string[]; // 敏感域名列表，验证失败时需要重定向，默认为空数组
  timeout?: number; // 请求超时时间（毫秒），默认为10000ms
}

export interface SignInOrUpResponse {
  success: boolean;
  message?: string;
  user_id?: string;
  isNewUser?: boolean;
}

export interface AuthMiddlewareOptions {
  // 非敏感配置 - 可以有合理默认值
  publicPaths?: string[]; // 公开路径列表，默认为空数组
  fallbackUrl?: string; // 不安全URL的fallback地址，默认为"/"
  // 注意：敏感配置（authServiceRedirectUrl、authDomain）强制通过环境变量配置，不在此接口中提供
}

export interface VerifyStatusResponse {
  success: boolean;
  message: string;
  statusCode?: number;
}

export interface SSORedirectState {
  isSSORedirect: boolean;
  clear: () => void;
}
